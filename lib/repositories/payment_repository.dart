import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

enum PaymentProvider {
  simulation,
  stripe,
  // Add other providers as needed
}

class PaymentResult {
  final bool isSuccess;
  final String? transactionId;
  final String? errorMessage;
  final Map<String, dynamic>? metadata;

  const PaymentResult({
    required this.isSuccess,
    this.transactionId,
    this.errorMessage,
    this.metadata,
  });

  factory PaymentResult.success({
    required String transactionId,
    Map<String, dynamic>? metadata,
  }) {
    return PaymentResult(
      isSuccess: true,
      transactionId: transactionId,
      metadata: metadata,
    );
  }

  factory PaymentResult.failure({
    required String errorMessage,
    Map<String, dynamic>? metadata,
  }) {
    return PaymentResult(
      isSuccess: false,
      errorMessage: errorMessage,
      metadata: metadata,
    );
  }
}

class WithdrawalResult {
  final bool isSuccess;
  final String? withdrawalId;
  final String? errorMessage;
  final DateTime? estimatedArrival;
  final Map<String, dynamic>? metadata;

  const WithdrawalResult({
    required this.isSuccess,
    this.withdrawalId,
    this.errorMessage,
    this.estimatedArrival,
    this.metadata,
  });

  factory WithdrawalResult.success({
    required String withdrawalId,
    DateTime? estimatedArrival,
    Map<String, dynamic>? metadata,
  }) {
    return WithdrawalResult(
      isSuccess: true,
      withdrawalId: withdrawalId,
      estimatedArrival: estimatedArrival,
      metadata: metadata,
    );
  }

  factory WithdrawalResult.failure({
    required String errorMessage,
    Map<String, dynamic>? metadata,
  }) {
    return WithdrawalResult(
      isSuccess: false,
      errorMessage: errorMessage,
      metadata: metadata,
    );
  }
}

abstract class PaymentProcessor {
  Future<void> initialize();
  Future<PaymentResult> processPayment({
    required double amount,
    required String currency,
    String? paymentMethodId,
    Map<String, dynamic>? metadata,
  });
  Future<WithdrawalResult> processWithdrawal({
    required double amount,
    required String bankAccount,
    Map<String, dynamic>? metadata,
  });
  Future<String?> createPaymentMethod();
  bool get isInitialized;
}

class SimulationPaymentProcessor implements PaymentProcessor {
  bool _isInitialized = false;
  final Random _random = Random();

  @override
  bool get isInitialized => _isInitialized;

  @override
  Future<void> initialize() async {
    await Future.delayed(const Duration(milliseconds: 500));
    _isInitialized = true;
  }

  @override
  Future<PaymentResult> processPayment({
    required double amount,
    required String currency,
    String? paymentMethodId,
    Map<String, dynamic>? metadata,
  }) async {
    // Simulate processing time
    await Future.delayed(Duration(milliseconds: 1000 + _random.nextInt(2000)));

    // Simulate 95% success rate
    final isSuccess = _random.nextInt(100) < 95;

    if (isSuccess) {
      return PaymentResult.success(
        transactionId:
            'sim_${DateTime.now().millisecondsSinceEpoch}_${_random.nextInt(10000)}',
        metadata: {
          'processor': 'simulation',
          'amount': amount,
          'currency': currency,
          'paymentMethodId': paymentMethodId,
          'processedAt': DateTime.now().toIso8601String(),
        },
      );
    } else {
      final errorMessages = [
        'Card declined',
        'Insufficient funds',
        'Network error',
        'Invalid card details',
        'Payment processor unavailable',
      ];
      return PaymentResult.failure(
        errorMessage: errorMessages[_random.nextInt(errorMessages.length)],
        metadata: {
          'processor': 'simulation',
          'amount': amount,
          'currency': currency,
        },
      );
    }
  }

  @override
  Future<WithdrawalResult> processWithdrawal({
    required double amount,
    required String bankAccount,
    Map<String, dynamic>? metadata,
  }) async {
    // Simulate processing time
    await Future.delayed(Duration(milliseconds: 1500 + _random.nextInt(1500)));

    // Simulate 98% success rate for withdrawals
    final isSuccess = _random.nextInt(100) < 98;

    if (isSuccess) {
      return WithdrawalResult.success(
        withdrawalId:
            'wd_${DateTime.now().millisecondsSinceEpoch}_${_random.nextInt(10000)}',
        estimatedArrival: DateTime.now().add(
          Duration(days: 1 + _random.nextInt(3)),
        ),
        metadata: {
          'processor': 'simulation',
          'amount': amount,
          'bankAccount': bankAccount,
          'processedAt': DateTime.now().toIso8601String(),
        },
      );
    } else {
      return WithdrawalResult.failure(
        errorMessage: 'Withdrawal failed: Invalid bank account details',
        metadata: {
          'processor': 'simulation',
          'amount': amount,
          'bankAccount': bankAccount,
        },
      );
    }
  }

  @override
  Future<String?> createPaymentMethod() async {
    await Future.delayed(Duration(milliseconds: 500 + _random.nextInt(1000)));
    return 'pm_sim_${DateTime.now().millisecondsSinceEpoch}_${_random.nextInt(10000)}';
  }
}

class StripePaymentProcessor implements PaymentProcessor {
  bool _isInitialized = false;

  // Stripe configuration - replace with your actual keys
  static const String publishableKey =
      kDebugMode
          ? 'pk_test_51234567890123456789012345678901234567890123456789012345678901234567890'
          : 'pk_live_your_live_publishable_key';

  // Backend endpoint for creating payment intents
  static const String backendUrl =
      kDebugMode
          ? 'http://localhost:3000/api' // Your local backend for testing
          : 'https://your-production-backend.com/api';

  @override
  bool get isInitialized => _isInitialized;

  @override
  Future<void> initialize() async {
    try {
      // Only initialize Stripe on mobile platforms
      if (!kIsWeb) {
        Stripe.publishableKey = publishableKey;
        await Stripe.instance.applySettings();
      }
      _isInitialized = true;
    } catch (e) {
      debugPrint('Failed to initialize Stripe: $e');
      throw Exception('Stripe initialization failed: $e');
    }
  }

  @override
  Future<PaymentResult> processPayment({
    required double amount,
    required String currency,
    String? paymentMethodId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      if (!_isInitialized) {
        throw Exception('Stripe not initialized');
      }

      // Step 1: Create payment intent on your backend
      final paymentIntent = await _createPaymentIntent(
        amount: amount,
        currency: currency,
        metadata: metadata,
      );

      if (paymentIntent == null) {
        return PaymentResult.failure(
          errorMessage: 'Failed to create payment intent',
        );
      }

      // Step 2: Confirm payment with Stripe
      final result = await _confirmPayment(
        clientSecret: paymentIntent['client_secret'],
        paymentMethodId: paymentMethodId,
      );

      if (result.isSuccess) {
        return PaymentResult.success(
          transactionId: paymentIntent['id'],
          metadata: {
            'processor': 'stripe',
            'amount': amount,
            'currency': currency,
            'paymentIntentId': paymentIntent['id'],
            'processedAt': DateTime.now().toIso8601String(),
            ...?metadata,
          },
        );
      } else {
        return PaymentResult.failure(
          errorMessage: result.errorMessage ?? 'Payment failed',
          metadata: {
            'processor': 'stripe',
            'amount': amount,
            'currency': currency,
          },
        );
      }
    } catch (e) {
      debugPrint('Stripe payment error: $e');
      return PaymentResult.failure(
        errorMessage: 'Payment processing failed: ${e.toString()}',
      );
    }
  }

  @override
  Future<WithdrawalResult> processWithdrawal({
    required double amount,
    required String bankAccount,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      if (!_isInitialized) {
        throw Exception('Stripe not initialized');
      }

      // For now, simulate withdrawal processing
      // In a real implementation, you would use Stripe Connect or similar
      await Future.delayed(const Duration(seconds: 2));

      return WithdrawalResult.success(
        withdrawalId: 'stripe_wd_${DateTime.now().millisecondsSinceEpoch}',
        estimatedArrival: DateTime.now().add(const Duration(days: 2)),
        metadata: {
          'processor': 'stripe',
          'amount': amount,
          'bankAccount': bankAccount,
          'processedAt': DateTime.now().toIso8601String(),
          ...?metadata,
        },
      );
    } catch (e) {
      debugPrint('Stripe withdrawal error: $e');
      return WithdrawalResult.failure(
        errorMessage: 'Withdrawal processing failed: ${e.toString()}',
      );
    }
  }

  @override
  Future<String?> createPaymentMethod() async {
    try {
      if (!_isInitialized) {
        throw Exception('Stripe not initialized');
      }

      // For web, we'll return a mock payment method
      // For mobile, you would typically use Stripe's payment sheet
      if (kIsWeb) {
        // Simulate payment method creation for web
        await Future.delayed(const Duration(seconds: 1));
        return 'pm_stripe_${DateTime.now().millisecondsSinceEpoch}';
      }

      // For mobile, you would implement payment method creation
      // This is typically done through Stripe's payment sheet
      return 'pm_stripe_${DateTime.now().millisecondsSinceEpoch}';
    } catch (e) {
      debugPrint('Failed to create Stripe payment method: $e');
      return null;
    }
  }

  // Helper method to create payment intent on backend
  Future<Map<String, dynamic>?> _createPaymentIntent({
    required double amount,
    required String currency,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // Convert amount to cents
      final amountInCents = (amount * 100).toInt();

      // For demo purposes, simulate backend call
      // In production, replace this with actual backend call
      if (kDebugMode) {
        await Future.delayed(const Duration(seconds: 1));
        return {
          'id': 'pi_stripe_${DateTime.now().millisecondsSinceEpoch}',
          'client_secret':
              'pi_stripe_${DateTime.now().millisecondsSinceEpoch}_secret',
          'amount': amountInCents,
          'currency': currency,
          'status': 'requires_payment_method',
        };
      }

      // Production backend call
      final response = await http.post(
        Uri.parse('$backendUrl/create-payment-intent'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'amount': amountInCents,
          'currency': currency,
          'metadata': metadata,
        }),
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        debugPrint('Backend error: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('Error creating payment intent: $e');
      return null;
    }
  }

  // Helper method to confirm payment with Stripe
  Future<PaymentResult> _confirmPayment({
    required String clientSecret,
    String? paymentMethodId,
  }) async {
    try {
      if (kIsWeb) {
        // For web, simulate payment confirmation
        await Future.delayed(const Duration(seconds: 2));
        // Simulate 95% success rate
        final random = Random();
        if (random.nextInt(100) < 95) {
          return PaymentResult.success(transactionId: 'stripe_web_success');
        } else {
          return PaymentResult.failure(errorMessage: 'Payment declined');
        }
      }

      // For mobile, use actual Stripe confirmation
      final result = await Stripe.instance.confirmPayment(
        paymentIntentClientSecret: clientSecret,
        data: const PaymentMethodParams.card(
          paymentMethodData: PaymentMethodData(),
        ),
      );

      if (result.status == PaymentIntentsStatus.Succeeded) {
        return PaymentResult.success(transactionId: result.id);
      } else {
        return PaymentResult.failure(
          errorMessage: 'Payment failed: ${result.status}',
        );
      }
    } catch (e) {
      debugPrint('Payment confirmation error: $e');
      return PaymentResult.failure(
        errorMessage: 'Payment confirmation failed: ${e.toString()}',
      );
    }
  }
}

class PaymentRepository {
  static final PaymentRepository _instance = PaymentRepository._internal();
  factory PaymentRepository() => _instance;
  PaymentRepository._internal();

  PaymentProcessor? _processor;
  PaymentProvider _currentProvider = PaymentProvider.simulation;

  bool get isInitialized => _processor?.isInitialized ?? false;

  Future<void> initialize({PaymentProvider? provider}) async {
    _currentProvider = provider ?? PaymentProvider.simulation;

    switch (_currentProvider) {
      case PaymentProvider.simulation:
        _processor = SimulationPaymentProcessor();
        break;
      case PaymentProvider.stripe:
        _processor = StripePaymentProcessor();
        break;
    }

    await _processor?.initialize();
  }

  Future<PaymentResult> processPayment({
    required double amount,
    required String currency,
    String? paymentMethodId,
    Map<String, dynamic>? metadata,
  }) async {
    if (_processor == null || !_processor!.isInitialized) {
      return PaymentResult.failure(
        errorMessage: 'Payment processor not initialized',
      );
    }

    try {
      return await _processor!.processPayment(
        amount: amount,
        currency: currency,
        paymentMethodId: paymentMethodId,
        metadata: metadata,
      );
    } catch (e) {
      return PaymentResult.failure(
        errorMessage: 'Payment processing failed: $e',
      );
    }
  }

  Future<WithdrawalResult> processWithdrawal({
    required double amount,
    required String bankAccount,
    Map<String, dynamic>? metadata,
  }) async {
    if (_processor == null || !_processor!.isInitialized) {
      return WithdrawalResult.failure(
        errorMessage: 'Payment processor not initialized',
      );
    }

    try {
      return await _processor!.processWithdrawal(
        amount: amount,
        bankAccount: bankAccount,
        metadata: metadata,
      );
    } catch (e) {
      return WithdrawalResult.failure(
        errorMessage: 'Withdrawal processing failed: $e',
      );
    }
  }

  Future<String?> createPaymentMethod() async {
    if (_processor == null || !_processor!.isInitialized) {
      return null;
    }

    try {
      return await _processor!.createPaymentMethod();
    } catch (e) {
      debugPrint('Failed to create payment method: $e');
      return null;
    }
  }

  PaymentProvider get currentProvider => _currentProvider;

  Future<void> switchProvider(PaymentProvider provider) async {
    if (provider != _currentProvider) {
      await initialize(provider: provider);
    }
  }
}
