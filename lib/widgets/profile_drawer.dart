import 'dart:async';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:image_network/image_network.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:money_mouthy_two/controllers/category_controller.dart';
import 'package:money_mouthy_two/controllers/profile_controller.dart';
import 'package:money_mouthy_two/screens/edit_profile_screen.dart';
import 'package:money_mouthy_two/screens/wallet_screen.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:money_mouthy_two/services/post_service.dart';
import 'package:money_mouthy_two/services/follow_service.dart';

import 'package:money_mouthy_two/widgets/home/<USER>';
import '../controllers/wallet_controller.dart';
import '../services/post_amount_service.dart';

class ProfileDrawer extends StatefulWidget {
  final Function(int)? onCategoryChanged;
  final bool isMobileCompact;

  const ProfileDrawer({
    Key? key,
    this.onCategoryChanged,
    this.isMobileCompact = false,
  }) : super(key: key);

  @override
  State<ProfileDrawer> createState() => _ProfileDrawerState();
}

class _ProfileDrawerState extends State<ProfileDrawer> {
  final User? _user = FirebaseAuth.instance.currentUser;
  final PostService _postService = PostService();
  final FollowService _followService = FollowService();
  final CategoryController _categoryController = Get.find<CategoryController>();
  final ProfileController _profileController = Get.find<ProfileController>();
  bool _isLoading = true;
  double _postAmount = 0.00;
  final WalletController walletController = Get.find<WalletController>();
  final PostAmountService _postAmountService = PostAmountService();

  @override
  void initState() {
    super.initState();
    _initializeWallet().then((_) {
      // Fetch user data after wallet is initialized
      _fetchUserData();
    });

    // Initialize post amount service with current value
    _postAmountService.initialize(_postAmount);

    // Wallet state changes are handled by GetX reactive widgets

    // Listen to post amount changes from other screens
    _postAmountService.postAmountStream.listen((amount) {
      if (mounted && _postAmount != amount) {
        final maxAmount = _getMaxSliderValue();
        final clampedAmount = amount.clamp(0.05, maxAmount);
        setState(() {
          _postAmount = clampedAmount;
        });
        // Update Firebase if the amount was clamped
        if (clampedAmount != amount) {
          _updatePostAmount(clampedAmount);
        }
      }
    });

    // Listen to wallet balance changes and adjust post amount if needed
    ever(walletController.balance.obs, (double balance) {
      if (mounted && _postAmount > balance && balance > 0) {
        final newAmount = (balance * 0.95).clamp(
          0.05,
          balance,
        ); // Use 95% of balance as max
        setState(() {
          _postAmount = newAmount;
        });
        _updatePostAmount(newAmount);
      }
    });

    // No need for ProfileUpdateService listener since we're using GetX reactive state
    // The Obx widgets will automatically update when ProfileController data changes
  }

  Future<void> _initializeWallet() async {
    try {
      if (!walletController.isInitialized) {
        await walletController.initialize();
      }
    } catch (e) {
      debugPrint('Error initializing wallet in profile drawer: $e');
    }
  }

  double _getMaxSliderValue() {
    // Limit slider to current wallet balance, with a minimum of $0.05
    final balance = walletController.balance;
    return balance > 0.05 ? balance : 0.05;
  }

  int _getSliderDivisions() {
    // Calculate divisions based on the max value (in 0.05 increments)
    final maxValue = _getMaxSliderValue();
    final minValue = 0.05;
    if (maxValue <= minValue) return 1;
    return ((maxValue - minValue) / 0.05).round().clamp(1, 100);
  }

  // Helper method to ensure post amount is always valid
  void _validateAndUpdatePostAmount() {
    final maxAmount = _getMaxSliderValue();
    if (_postAmount > maxAmount) {
      final newAmount = maxAmount.clamp(0.05, maxAmount);
      setState(() {
        _postAmount = newAmount;
      });
      _updatePostAmount(newAmount);
    }
  }

  @override
  void dispose() {
    // GetX handles reactive updates automatically
    super.dispose();
  }

  Future<void> _fetchUserData() async {
    if (_user == null) {
      setState(() => _isLoading = false);
      return;
    }
    try {
      final doc =
          await FirebaseFirestore.instance
              .collection('users')
              .doc(_user.uid)
              .get();
      if (mounted) {
        setState(() {
          if (doc.exists && doc.data()!.containsKey('postAmount')) {
            double savedAmount = (doc.data()!['postAmount'] as num).toDouble();
            // Ensure saved amount doesn't exceed current wallet balance and is at least minimum
            final maxAmount = _getMaxSliderValue();
            _postAmount = savedAmount.clamp(0.05, maxAmount);

            // If we had to clamp the value, update it in Firebase
            if (_postAmount != savedAmount) {
              _updatePostAmount(_postAmount);
            }
          } else {
            // Set default minimum amount if no saved amount
            _postAmount = 0.05;
          }
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Failed to fetch user data: $e');
      if (mounted) {
        setState(() {
          _postAmount = 0.05; // Set default on error
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _updatePostAmount(double value) async {
    if (_user == null) return;

    // Ensure value is within valid range
    final maxAmount = _getMaxSliderValue();
    final clampedValue = value.clamp(0.05, maxAmount);

    // Update local state if needed
    if (_postAmount != clampedValue) {
      setState(() {
        _postAmount = clampedValue;
      });
    }

    // Update the shared service to notify other screens
    _postAmountService.updatePostAmount(clampedValue);

    try {
      await FirebaseFirestore.instance.collection('users').doc(_user!.uid).set({
        'postAmount': clampedValue,
      }, SetOptions(merge: true));
    } catch (e) {
      debugPrint('Failed to update post amount: $e');
    }
  }

  Future<void> _onCategorySelected(int index) async {
    // Use CategoryController to update category
    await _categoryController.updateCategory(index);

    // Notify parent about category change (for backward compatibility)
    widget.onCategoryChanged?.call(index);
  }

  Future<Map<String, int>> _getUserStats() async {
    if (_user == null) {
      return {'posts': 0, 'followers': 0};
    }

    try {
      // Get posts count
      final postsQuery =
          await FirebaseFirestore.instance
              .collection('posts')
              .where('authorId', isEqualTo: _user!.uid)
              .get();
      final postsCount = postsQuery.docs.length;

      // Get followers count
      final followersQuery =
          await FirebaseFirestore.instance
              .collection('follows')
              .where('followingId', isEqualTo: _user!.uid)
              .get();
      final followersCount = followersQuery.docs.length;

      return {'posts': postsCount, 'followers': followersCount};
    } catch (e) {
      debugPrint('Error getting user stats: $e');
      return {'posts': 0, 'followers': 0};
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Container(
        color: Colors.grey.shade50,
        child: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_user == null) {
      return Container(
        color: Colors.grey.shade50,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('Could not load user data.'),
              TextButton(
                onPressed: () {
                  FirebaseAuth.instance.signOut();
                  Navigator.of(context).pushReplacementNamed('/login');
                },
                child: const Text('Logout'),
              ),
            ],
          ),
        ),
      );
    }

    // Return mobile compact layout if requested
    if (widget.isMobileCompact) {
      return Obx(() {
        debugPrint(
          'ProfileDrawer: Mobile compact Obx rebuilding with profile image URL: ${_profileController.profileImageUrl}',
        );
        return _buildMobileCompactLayout();
      });
    }

    return Container(
      color: Colors.grey.shade50,
      child: Column(
        children: [
          // Header Section with light purple background
          Container(
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(20, 50, 20, 25),
            decoration: const BoxDecoration(
              color: Color(0xFFE8E3FF), // Light purple/lavender background
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            child: Obx(() {
              debugPrint(
                'ProfileDrawer: Obx rebuilding with profile image URL: ${_profileController.profileImageUrl}',
              );
              return Row(
                children: [
                  // Profile avatar with edit icon
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (_) => const EditProfileScreen(),
                        ),
                      );
                    },
                    child: Stack(
                      children: [
                        CircleAvatar(
                          radius: 30,
                          backgroundColor: Colors.grey.shade300,
                          child:
                              _profileController.profileImageUrl.isNotEmpty
                                  ? ClipOval(
                                    child: ImageNetwork(
                                      image:
                                          '${_profileController.profileImageUrl}?t=${DateTime.now().millisecondsSinceEpoch}',
                                      height: 60,
                                      width: 60,
                                      duration: 500,
                                      curve: Curves.easeIn,
                                      onPointer: true,
                                      debugPrint: false,
                                      backgroundColor: Colors.white,
                                      fitAndroidIos: BoxFit.cover,
                                      fitWeb: BoxFitWeb.cover,
                                      borderRadius: BorderRadius.circular(70),
                                      onLoading:
                                          const CircularProgressIndicator(
                                            color: Colors.indigoAccent,
                                            strokeWidth: 0.1,
                                          ),
                                      onError: const Icon(
                                        Icons.person,
                                        color: Colors.blue,
                                      ),
                                    ),
                                  )
                                  : const Icon(
                                    Icons.person,
                                    size: 30,
                                    color: Colors.white,
                                  ),
                        ),
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: Container(
                            width: 20,
                            height: 20,
                            decoration: const BoxDecoration(
                              color: Color(0xFF5159FF),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.edit,
                              size: 12,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Name and stats
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Display name
                        Text(
                          _profileController.displayName.isNotEmpty
                              ? _profileController.displayName
                              : _profileController.username,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        // Username in gray
                        Text(
                          '@${_profileController.username}',
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 2),
                      ],
                    ),
                  ),
                ],
              );
            }),
          ),

          // Main content - scrollable
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Paid Post Section
                  _buildPaidPostSection(),
                  const SizedBox(height: 16),

                  // Post Amount Section
                  _buildPostAmountSection(),
                  const SizedBox(height: 16),

                  // Categories Section
                  _buildCategoriesSection(),
                  const SizedBox(height: 12),

                  // Connect Section
                  _buildConnectSection(),
                  const SizedBox(height: 16),

                  // Share Section
                  _buildShareSection(_profileController.username),
                  const SizedBox(height: 16),

                  // About Button
                  _buildAboutButton(context),
                  const SizedBox(height: 20),

                  // Bottom Actions Row (Logout and Delete Account)
                  _buildBottomActionsRow(context, _profileController.username),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaidPostSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Paid Post container with border
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Current Amount',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              ),
              Obx(
                () => Text(
                  walletController.formatCurrency(walletController.balance),
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),

        // Fund Account text
        const Text(
          'Fund Account',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 6),

        // ReUp! button
        SizedBox(
          height: 30,
          child: ElevatedButton.icon(
            onPressed: () {
              Navigator.of(
                context,
              ).push(MaterialPageRoute(builder: (_) => const WalletScreen()));
            },
            icon: const Icon(Icons.add, size: 15),
            label: const Text(
              'ReUp!',
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF5159FF),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPostAmountSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Increase post amount',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: const Color(0xFF5159FF),
            inactiveTrackColor: Colors.grey.shade300,
            thumbColor: const Color(0xFF5159FF),
            overlayColor: const Color(0xFF5159FF).withValues(alpha: 0.2),
            trackHeight: 4.0,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8.0),
          ),
          child: Obx(() {
            final maxValue = _getMaxSliderValue();
            final currentValue = _postAmount.clamp(0.05, maxValue);

            return Slider(
              value: currentValue,
              min: 0.05,
              max: maxValue,
              divisions: _getSliderDivisions(),
              onChanged: (value) {
                setState(() {
                  _postAmount = value;
                });
              },
              onChangeEnd: (value) {
                _updatePostAmount(value);
              },
            );
          }),
        ),
        Center(
          child: Text(
            '\$${_postAmount.toStringAsFixed(2)}',
            style: const TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoriesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Categories',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 6),
        Obx(
          () => Wrap(
            spacing: 8,
            runSpacing: 8,
            children:
                Categories.all.asMap().entries.map((entry) {
                  final index = entry.key;
                  final category = entry.value;
                  final isSelected =
                      index == _categoryController.selectedCategoryIndex;

                  return GestureDetector(
                    onTap: () => _onCategorySelected(index),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color:
                            isSelected ? category.color : Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color:
                              isSelected
                                  ? category.color
                                  : Colors.grey.shade300,
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Text(
                        category.name,
                        style: TextStyle(
                          color: isSelected ? Colors.white : Colors.black87,
                          fontSize: 12,
                          fontWeight:
                              isSelected ? FontWeight.w600 : FontWeight.w500,
                        ),
                      ),
                    ),
                  );
                }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildConnectSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // const Text(
        //   'Connect with others',
        //   style: TextStyle(
        //     fontSize: 14,
        //     fontWeight: FontWeight.w500,
        //     color: Colors.black87,
        //   ),
        // ),
        // const SizedBox(height: 6),
        // SizedBox(
        //   width: double.infinity,
        //   height: 40,
        //   child: ElevatedButton.icon(
        //     onPressed: () {
        //       Navigator.of(
        //         context,
        //       ).push(MaterialPageRoute(builder: (_) => ConnectScreen()));
        //     },
        //     icon: const Icon(Icons.people, size: 20),
        //     label: const Text(
        //       'Connect',
        //       style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        //     ),
        //     style: ElevatedButton.styleFrom(
        //       backgroundColor: const Color(0xFF5159FF),
        //       foregroundColor: Colors.white,
        //       shape: RoundedRectangleBorder(
        //         borderRadius: BorderRadius.circular(12),
        //       ),
        //       elevation: 0,
        //     ),
        //   ),
        // ),
        Divider(color: Colors.grey.shade300, thickness: 0.5, height: 24),
        FutureBuilder<Map<String, int>>(
          future: _getUserStats(),
          builder: (context, snapshot) {
            if (snapshot.hasData) {
              final stats = snapshot.data!;
              return Text(
                '${stats['posts']} posts • ${stats['followers']} followers',
                style: const TextStyle(fontSize: 14, color: Colors.black),
              );
            }
            return const Text(
              'Loading stats...',
              style: TextStyle(fontSize: 12, color: Colors.black54),
            );
          },
        ),
        //  Divider
        Divider(color: Colors.grey.shade300, thickness: 0.5, height: 24),
      ],
    );
  }

  Widget _buildShareSection(String username) {
    final Map<String, IconData> socialIcons = {
      'Facebook': FontAwesomeIcons.facebook,
      'Twitter': FontAwesomeIcons.twitter,
      'Instagram': FontAwesomeIcons.instagram,
      'Pinterest': FontAwesomeIcons.pinterest,
      'LinkedIn': FontAwesomeIcons.linkedin,
      'TikTok': FontAwesomeIcons.tiktok,
      'YouTube': FontAwesomeIcons.youtube,
      'WhatsApp': FontAwesomeIcons.whatsapp,
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Share to others',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              socialIcons.entries.map((entry) {
                return Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    onPressed: () async {
                      final url = _buildSocialUrl(entry.key, username);
                      if (await canLaunchUrl(Uri.parse(url))) {
                        await launchUrl(
                          Uri.parse(url),
                          mode: LaunchMode.externalApplication,
                        );
                      }
                    },
                    icon: FaIcon(entry.value, size: 16, color: Colors.black54),
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }

  String _buildSocialUrl(String platform, String username) {
    final encodedUsername = Uri.encodeComponent(username);
    switch (platform) {
      case 'Facebook':
        return 'https://www.facebook.com/sharer/sharer.php?u=https://moneymouthy.app/user/$encodedUsername';
      case 'Twitter':
        return 'https://twitter.com/intent/tweet?text=Check%20out%20my%20profile%20on%20Money%20Mouthy%20%40$encodedUsername%20https://moneymouthy.app';
      case 'Instagram':
        return 'https://www.instagram.com';
      case 'Pinterest':
        return 'https://www.pinterest.com/pin/create/button/?url=https://moneymouthy.app/user/$encodedUsername';
      case 'LinkedIn':
        return 'https://www.linkedin.com/sharing/share-offsite/?url=https://moneymouthy.app/user/$encodedUsername';
      case 'TikTok':
        return 'https://www.tiktok.com';
      case 'YouTube':
        return 'https://www.youtube.com';
      case 'WhatsApp':
        return 'https://wa.me/?text=Check%20out%20my%20profile%20on%20Money%20Mouthy%20https://moneymouthy.app/user/$encodedUsername';
      default:
        return 'https://moneymouthy.app';
    }
  }

  Widget _buildAboutButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 40,
      child: OutlinedButton.icon(
        onPressed: () {
          Navigator.of(context).pushNamed('/about');
        },
        icon: const Icon(Icons.info_outline, size: 20),
        label: const Text(
          'About',
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
        style: OutlinedButton.styleFrom(
          foregroundColor: const Color(0xFF5159FF),
          side: const BorderSide(color: Color(0xFF5159FF)),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
    );
  }

  Widget _buildBottomActionsRow(BuildContext context, String username) {
    return Row(
      children: [
        // Logout Button
        Expanded(
          child: TextButton(
            onPressed: () => _handleLogout(context),
            child: const Text(
              'Logout',
              style: TextStyle(
                color: Color(0xFF5159FF),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        // Delete Account Button
        Expanded(
          child: TextButton(
            onPressed: () => _handleDeleteAccount(context, username),
            child: const Text(
              'Delete Account',
              style: TextStyle(
                color: Colors.red,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleLogout(BuildContext context) async {
    final shouldLogout =
        await showDialog<bool>(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('Logout'),
                content: const Text('Are you sure you want to logout?'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    child: const Text('Logout'),
                  ),
                ],
              ),
        ) ??
        false;

    if (shouldLogout) {
      try {
        await FirebaseAuth.instance.signOut();
        if (mounted && context.mounted) {
          Navigator.of(context).pushReplacementNamed('/login');
        }
      } catch (e) {
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('Logout failed: $e')));
        }
      }
    }
  }

  Future<void> _handleDeleteAccount(
    BuildContext context,
    String username,
  ) async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Account'),
          content: const Text(
            'Are you sure you want to delete your account? This action cannot be undone.',
          ),
          actions: [
            TextButton(
              child: const Text('Cancel'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
              onPressed: () async {
                Navigator.of(context).pop();
                try {
                  final user = FirebaseAuth.instance.currentUser;
                  if (user != null) {
                    await FirebaseFirestore.instance
                        .collection('users')
                        .doc(user.uid)
                        .delete();
                    await FirebaseFirestore.instance
                        .collection('usernames')
                        .doc(username)
                        .delete();
                    await user.delete();
                  }
                  if (mounted && context.mounted) {
                    Navigator.of(context).pushReplacementNamed('/login');
                  }
                } catch (e) {
                  if (mounted && context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Failed to delete account: $e')),
                    );
                  }
                }
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildMobileCompactLayout() {
    return Container(
      color: Colors.grey.shade50,
      child: Column(
        children: [
          // Header Section with profile info
          Container(
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(12, 50, 12, 20),
            decoration: const BoxDecoration(
              color: Color(0xFFE8E3FF), // Light purple/lavender background
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                // Profile image at the top
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (_) => const EditProfileScreen(),
                      ),
                    );
                  },
                  child: CircleAvatar(
                    radius: 25,
                    backgroundColor: Colors.grey.shade300,
                    child: ClipOval(
                      child: ImageNetwork(
                        image:
                            _profileController.profileImageUrl.isNotEmpty
                                ? '${_profileController.profileImageUrl}?t=${DateTime.now().millisecondsSinceEpoch}'
                                : '',
                        height: 50,
                        width: 50,
                        duration: 500,
                        curve: Curves.easeIn,
                        onPointer: true,
                        debugPrint: false,
                        backgroundColor: Colors.white,
                        fitAndroidIos: BoxFit.cover,
                        fitWeb: BoxFitWeb.cover,
                        borderRadius: BorderRadius.circular(70),
                        onLoading: const CircularProgressIndicator(
                          color: Colors.indigoAccent,
                          strokeWidth: 0.1,
                        ),
                        onError: const Icon(Icons.person, color: Colors.blue),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                // Display name prominently
                Text(
                  _profileController.displayName.isNotEmpty
                      ? _profileController.displayName
                      : _profileController.username,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                // Username below in gray text
                Text(
                  '@${_profileController.username}',
                  style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                // User's wallet balance
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFF5159FF).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Obx(
                    () => Text(
                      walletController.formatCurrency(walletController.balance),
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF5159FF),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Main content - scrollable
          Expanded(
            child: Wrap(
              children: [
                // Category selection slider/picker
                _buildCompactCategoriesSection(),
                const SizedBox(height: 16),

                // Post Amount Section (compact)
                _buildCompactPostAmountSection(),
                const SizedBox(height: 16),

                // Other items with icons and horizontal scroll for social
                _buildCompactActionsSection(_profileController.username),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactCategoriesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Category',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Obx(
          () => SizedBox(
            height: 35,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: Categories.all.length,
              itemBuilder: (context, index) {
                final category = Categories.all[index];
                final isSelected =
                    index == _categoryController.selectedCategoryIndex;

                return GestureDetector(
                  onTap: () => _onCategorySelected(index),
                  child: Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color:
                          isSelected
                              ? const Color(0xFF5159FF)
                              : Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(18),
                    ),
                    child: Center(
                      child: Text(
                        category.name,
                        style: TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                          color: isSelected ? Colors.white : Colors.black87,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCompactPostAmountSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Post Amount',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Amount:',
                    style: TextStyle(fontSize: 11, fontWeight: FontWeight.w500),
                  ),
                  Text(
                    walletController.formatCurrency(_postAmount),
                    style: const TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF5159FF),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: const Color(0xFF5159FF),
                  inactiveTrackColor: Colors.grey.shade300,
                  thumbColor: const Color(0xFF5159FF),
                  overlayColor: const Color(0xFF5159FF).withValues(alpha: 0.2),
                  trackHeight: 3.0,
                  thumbShape: const RoundSliderThumbShape(
                    enabledThumbRadius: 6.0,
                  ),
                ),
                child: Obx(() {
                  final maxValue = _getMaxSliderValue();
                  final currentValue = _postAmount.clamp(0.05, maxValue);

                  return Slider(
                    value: currentValue,
                    min: 0.05,
                    max: maxValue,
                    divisions: _getSliderDivisions(),
                    onChanged: (value) {
                      setState(() {
                        _postAmount = value;
                      });
                      _updatePostAmount(value);
                    },
                  );
                }),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCompactActionsSection(String username) {
    return Column(
      children: [
        // Social icons with horizontal scroll
        SizedBox(
          height: 40,
          child: ListView(
            scrollDirection: Axis.horizontal,
            children: [
              _buildCompactActionIcon(
                FontAwesomeIcons.twitter,
                'Twitter',
                () => _launchUrl(
                  'https://twitter.com/intent/tweet?text=Check out Money Mouthy! A platform where your voice has value. Join me @$username',
                ),
              ),
              _buildCompactActionIcon(
                FontAwesomeIcons.facebook,
                'Facebook',
                () => _launchUrl(
                  'https://www.facebook.com/sharer/sharer.php?u=https://moneymouthy.com',
                ),
              ),
              _buildCompactActionIcon(
                FontAwesomeIcons.instagram,
                'Instagram',
                () => _launchUrl('https://instagram.com'),
              ),
              _buildCompactActionIcon(
                FontAwesomeIcons.linkedin,
                'LinkedIn',
                () => _launchUrl(
                  'https://www.linkedin.com/sharing/share-offsite/?url=https://moneymouthy.com',
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // About and Logout icons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildCompactActionIcon(
              Icons.info_outline,
              'About',
              () => _showAboutDialog(context),
            ),
            _buildCompactActionIcon(Icons.logout, 'Logout', () {
              FirebaseAuth.instance.signOut();
              Navigator.of(context).pushReplacementNamed('/login');
            }, color: Colors.red),
          ],
        ),
      ],
    );
  }

  Widget _buildCompactActionIcon(
    IconData icon,
    String label,
    VoidCallback onTap, {
    Color? color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(right: 12),
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16, color: color ?? Colors.grey[600]),
            const SizedBox(height: 2),
            Text(
              label,
              style: TextStyle(fontSize: 8, color: color ?? Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _launchUrl(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    }
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('About Money Mouthy'),
            content: const Text(
              'Money Mouthy is a platform where your voice has value. Share your thoughts, engage with others, and earn money for your content.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }
}
