"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getStripeConfig = exports.healthCheck = exports.stripeWebhook = exports.createPaymentIntent = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
const stripe_1 = __importDefault(require("stripe"));
const cors_1 = __importDefault(require("cors"));
// Initialize Firebase Admin
admin.initializeApp();
// Initialize Stripe with your secret key
const stripe = new stripe_1.default(functions.config().stripe.secret_key, {
    apiVersion: "2023-10-16",
});
// CORS configuration
const corsHandler = (0, cors_1.default)({ origin: true });
// Create Payment Intent - Allow unauthenticated access
exports.createPaymentIntent = functions.https.onRequest(async (req, res) => {
    return corsHandler(req, res, async () => {
        try {
            // Only allow POST requests
            if (req.method !== "POST") {
                res.status(405).json({ error: "Method not allowed" });
                return;
            }
            const { amount, currency = "usd", metadata = {} } = req.body;
            // Validate required fields
            if (!amount || typeof amount !== "number" || amount <= 0) {
                res.status(400).json({ error: "Invalid amount" });
                return;
            }
            // Validate amount limits (minimum $0.50, maximum $999,999)
            if (amount < 50 || amount > 99999900) {
                res.status(400).json({
                    error: "Amount must be between $0.50 and $999,999.00",
                });
                return;
            }
            // Create payment intent
            const paymentIntent = await stripe.paymentIntents.create({
                amount: Math.round(amount),
                currency: currency.toLowerCase(),
                metadata: Object.assign(Object.assign({}, metadata), { created_by: "money_mouthy_app", timestamp: new Date().toISOString() }),
                automatic_payment_methods: {
                    enabled: true,
                },
            });
            // Log successful payment intent creation
            console.log(`Payment intent created: ${paymentIntent.id} for amount: ${amount}`);
            res.status(200).json({
                id: paymentIntent.id,
                client_secret: paymentIntent.client_secret,
                amount: paymentIntent.amount,
                currency: paymentIntent.currency,
                status: paymentIntent.status,
            });
        }
        catch (error) {
            console.error("Error creating payment intent:", error);
            if (error instanceof stripe_1.default.errors.StripeError) {
                res.status(400).json({
                    error: `Stripe error: ${error.message}`,
                    type: error.type,
                });
            }
            else {
                res.status(500).json({
                    error: "Internal server error",
                });
            }
        }
    });
});
// Webhook to handle Stripe events
exports.stripeWebhook = functions.https.onRequest(async (req, res) => {
    try {
        // Only allow POST requests
        if (req.method !== "POST") {
            res.status(405).send("Method not allowed");
            return;
        }
        const sig = req.headers["stripe-signature"];
        const endpointSecret = functions.config().stripe.webhook_secret;
        let event;
        try {
            event = stripe.webhooks.constructEvent(req.rawBody, sig, endpointSecret);
        }
        catch (err) {
            console.error("Webhook signature verification failed:", err);
            res.status(400).send(`Webhook Error: ${err}`);
            return;
        }
        // Handle the event
        switch (event.type) {
            case "payment_intent.succeeded":
                const paymentIntent = event.data.object;
                await handleSuccessfulPayment(paymentIntent);
                break;
            case "payment_intent.payment_failed":
                const failedPayment = event.data.object;
                await handleFailedPayment(failedPayment);
                break;
            default:
                console.log(`Unhandled event type: ${event.type}`);
        }
        res.status(200).json({ received: true });
    }
    catch (error) {
        console.error("Webhook error:", error);
        res.status(500).json({ error: "Webhook processing failed" });
    }
});
// Handle successful payment
async function handleSuccessfulPayment(paymentIntent) {
    try {
        console.log(`Payment succeeded: ${paymentIntent.id}`);
        // Here you can add additional logic like:
        // - Update user's wallet balance in Firestore
        // - Send confirmation email
        // - Log transaction details
        // - Update analytics
        // Example: Log to Firestore
        await admin.firestore().collection("payment_logs").add({
            paymentIntentId: paymentIntent.id,
            amount: paymentIntent.amount,
            currency: paymentIntent.currency,
            status: "succeeded",
            metadata: paymentIntent.metadata,
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
        });
    }
    catch (error) {
        console.error("Error handling successful payment:", error);
    }
}
// Handle failed payment
async function handleFailedPayment(paymentIntent) {
    try {
        console.log(`Payment failed: ${paymentIntent.id}`);
        // Log failed payment
        await admin.firestore().collection("payment_logs").add({
            paymentIntentId: paymentIntent.id,
            amount: paymentIntent.amount,
            currency: paymentIntent.currency,
            status: "failed",
            metadata: paymentIntent.metadata,
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
        });
    }
    catch (error) {
        console.error("Error handling failed payment:", error);
    }
}
// Health check endpoint
exports.healthCheck = functions.https.onRequest((req, res) => {
    res.status(200).json({
        status: "healthy",
        timestamp: new Date().toISOString(),
        version: "1.0.0",
    });
});
// Get Stripe publishable key (for client configuration)
exports.getStripeConfig = functions.https.onRequest(async (req, res) => {
    return corsHandler(req, res, async () => {
        try {
            res.status(200).json({
                publishableKey: functions.config().stripe.publishable_key,
                currency: "usd",
                country: "US",
            });
        }
        catch (error) {
            console.error("Error getting Stripe config:", error);
            res.status(500).json({ error: "Failed to get configuration" });
        }
    });
});
//# sourceMappingURL=index.js.map