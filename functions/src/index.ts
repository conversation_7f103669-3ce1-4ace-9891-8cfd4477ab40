import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import Stripe from 'stripe';
import * as cors from 'cors';

// Initialize Firebase Admin
admin.initializeApp();

// Initialize Stripe with your secret key
const stripe = new Stripe(functions.config().stripe.secret_key, {
  apiVersion: '2023-10-16',
});

// CORS configuration
const corsHandler = cors({ origin: true });

// Create Payment Intent
export const createPaymentIntent = functions.https.onRequest(async (req, res) => {
  return corsHandler(req, res, async () => {
    try {
      // Only allow POST requests
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      const { amount, currency = 'usd', metadata = {} } = req.body;

      // Validate required fields
      if (!amount || typeof amount !== 'number' || amount <= 0) {
        res.status(400).json({ error: 'Invalid amount' });
        return;
      }

      // Validate amount limits (minimum $0.50, maximum $999,999)
      if (amount < 50 || amount > 99999900) {
        res.status(400).json({ 
          error: 'Amount must be between $0.50 and $999,999.00' 
        });
        return;
      }

      // Create payment intent
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount), // Ensure integer
        currency: currency.toLowerCase(),
        metadata: {
          ...metadata,
          created_by: 'money_mouthy_app',
          timestamp: new Date().toISOString(),
        },
        automatic_payment_methods: {
          enabled: true,
        },
      });

      // Log successful payment intent creation
      console.log(`Payment intent created: ${paymentIntent.id} for amount: ${amount}`);

      res.status(200).json({
        id: paymentIntent.id,
        client_secret: paymentIntent.client_secret,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        status: paymentIntent.status,
      });

    } catch (error) {
      console.error('Error creating payment intent:', error);
      
      if (error instanceof Stripe.errors.StripeError) {
        res.status(400).json({ 
          error: `Stripe error: ${error.message}`,
          type: error.type 
        });
      } else {
        res.status(500).json({ 
          error: 'Internal server error' 
        });
      }
    }
  });
});

// Webhook to handle Stripe events
export const stripeWebhook = functions.https.onRequest(async (req, res) => {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      res.status(405).send('Method not allowed');
      return;
    }

    const sig = req.headers['stripe-signature'] as string;
    const endpointSecret = functions.config().stripe.webhook_secret;

    let event: Stripe.Event;

    try {
      event = stripe.webhooks.constructEvent(req.rawBody, sig, endpointSecret);
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      res.status(400).send(`Webhook Error: ${err}`);
      return;
    }

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        await handleSuccessfulPayment(paymentIntent);
        break;
      
      case 'payment_intent.payment_failed':
        const failedPayment = event.data.object as Stripe.PaymentIntent;
        await handleFailedPayment(failedPayment);
        break;
      
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    res.status(200).json({ received: true });

  } catch (error) {
    console.error('Webhook error:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

// Handle successful payment
async function handleSuccessfulPayment(paymentIntent: Stripe.PaymentIntent) {
  try {
    console.log(`Payment succeeded: ${paymentIntent.id}`);
    
    // Here you can add additional logic like:
    // - Update user's wallet balance in Firestore
    // - Send confirmation email
    // - Log transaction details
    // - Update analytics
    
    // Example: Log to Firestore
    await admin.firestore().collection('payment_logs').add({
      paymentIntentId: paymentIntent.id,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      status: 'succeeded',
      metadata: paymentIntent.metadata,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    });

  } catch (error) {
    console.error('Error handling successful payment:', error);
  }
}

// Handle failed payment
async function handleFailedPayment(paymentIntent: Stripe.PaymentIntent) {
  try {
    console.log(`Payment failed: ${paymentIntent.id}`);
    
    // Log failed payment
    await admin.firestore().collection('payment_logs').add({
      paymentIntentId: paymentIntent.id,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      status: 'failed',
      metadata: paymentIntent.metadata,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    });

  } catch (error) {
    console.error('Error handling failed payment:', error);
  }
}

// Health check endpoint
export const healthCheck = functions.https.onRequest((req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Get Stripe publishable key (for client configuration)
export const getStripeConfig = functions.https.onRequest(async (req, res) => {
  return corsHandler(req, res, async () => {
    try {
      res.status(200).json({
        publishableKey: functions.config().stripe.publishable_key,
        currency: 'usd',
        country: 'US'
      });
    } catch (error) {
      console.error('Error getting Stripe config:', error);
      res.status(500).json({ error: 'Failed to get configuration' });
    }
  });
});
